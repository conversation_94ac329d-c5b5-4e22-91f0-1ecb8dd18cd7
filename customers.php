<?php
require_once 'database/conn.php';
require_once 'check_session.php';

// Require user to be logged in to access customers
requireLogin();

// Optional: Require specific role for customer management (uncomment if needed)
// requireRole('Staff'); // Staff and above can manage customers

// Create customers table if it doesn't exist
try {
    $create_table_sql = "
    CREATE TABLE IF NOT EXISTS customers (
        id INT AUTO_INCREMENT PRIMARY KEY,
        customer_name VARCHAR(255) NOT NULL,
        email VARCHAR(255) UNIQUE NOT NULL,
        phone VARCHAR(20),
        address TEXT,
        customer_group ENUM('Retail', 'Wholesale', 'VIP') DEFAULT 'Retail',
        status ENUM('Active', 'Inactive', 'Blocked') DEFAULT 'Active',
        total_spent DECIMAL(10,2) DEFAULT 0.00,
        registration_date DATETIME DEFAULT CURRENT_TIMESTAMP,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    $pdo->exec($create_table_sql);
} catch (PDOException $e) {
    error_log("Error creating customers table: " . $e->getMessage());
}

// Handle form submission for adding/editing customers
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        // Check if this is an edit request
        if (isset($_POST['action']) && $_POST['action'] === 'edit' && isset($_POST['id'])) {
            // Handle edit customer
            $customer_id = intval($_POST['id']);
            $customer_name = trim($_POST['customer_name'] ?? '');
            $email = trim($_POST['email'] ?? '');
            $phone = trim($_POST['phone'] ?? '');
            $address = trim($_POST['address'] ?? '');
            $customer_group = trim($_POST['customer_group'] ?? '');
            $status = trim($_POST['status'] ?? '');

            // Server-side validation for edit
            $errors = [];
            if (empty($customer_name)) $errors[] = 'Customer name is required';
            if (empty($email)) $errors[] = 'Email is required';
            if (!filter_var($email, FILTER_VALIDATE_EMAIL)) $errors[] = 'Valid email is required';
            if (empty($customer_group)) $errors[] = 'Customer group is required';
            if (empty($status)) $errors[] = 'Status is required';

            // Validate customer group
            $valid_groups = ['Retail', 'Wholesale', 'VIP'];
            if (!empty($customer_group) && !in_array($customer_group, $valid_groups)) {
                $errors[] = 'Invalid customer group';
            }

            // Validate status
            $valid_statuses = ['Active', 'Inactive', 'Blocked'];
            if (!empty($status) && !in_array($status, $valid_statuses)) {
                $errors[] = 'Invalid status';
            }

            if (!empty($errors)) {
                $message = implode(', ', $errors);
                $messageType = 'error';
            } else {
                // Check for duplicate email (excluding current customer)
                $check_stmt = $pdo->prepare("SELECT id FROM customers WHERE email = ? AND id != ?");
                $check_stmt->execute([$email, $customer_id]);
                if ($check_stmt->rowCount() > 0) {
                    $message = 'Email already exists!';
                    $messageType = 'error';
                } else {
                    $update_stmt = $pdo->prepare("
                        UPDATE customers SET customer_name = ?, email = ?, phone = ?, address = ?, customer_group = ?, status = ?, updated_at = NOW()
                        WHERE id = ?
                    ");
                    $result = $update_stmt->execute([$customer_name, $email, $phone, $address, $customer_group, $status, $customer_id]);

                    if ($result) {
                        $message = 'Customer updated successfully!';
                        $messageType = 'success';
                    } else {
                        $message = 'Error updating customer. Please try again.';
                        $messageType = 'error';
                    }
                }
            }
        } elseif (isset($_POST['action']) && $_POST['action'] === 'delete' && isset($_POST['id'])) {
            // Handle delete customer
            $customer_id = intval($_POST['id']);

            if ($customer_id <= 0) {
                $message = 'Invalid customer ID.';
                $messageType = 'error';
            } else {
                // First check if customer exists
                $check_stmt = $pdo->prepare("SELECT customer_name FROM customers WHERE id = ?");
                $check_stmt->execute([$customer_id]);
                $customer = $check_stmt->fetch();

                if (!$customer) {
                    $message = 'Customer not found.';
                    $messageType = 'error';
                } else {
                    // Delete the customer
                    $delete_stmt = $pdo->prepare("DELETE FROM customers WHERE id = ?");
                    $result = $delete_stmt->execute([$customer_id]);

                    if ($result) {
                        $message = 'Customer "' . htmlspecialchars($customer['customer_name']) . '" deleted successfully!';
                        $messageType = 'success';
                    } else {
                        $message = 'Error deleting customer. Please try again.';
                        $messageType = 'error';
                    }
                }
            }
        } else {
            // Handle add new customer
            $customer_name = trim($_POST['customer_name'] ?? '');
            $email = trim($_POST['email'] ?? '');
            $phone = trim($_POST['phone'] ?? '');
            $address = trim($_POST['address'] ?? '');
            $customer_group = trim($_POST['customer_group'] ?? '');
            $status = trim($_POST['status'] ?? '');

            // Server-side validation
            $errors = [];
            if (empty($customer_name)) $errors[] = 'Customer name is required';
            if (empty($email)) $errors[] = 'Email is required';
            if (!filter_var($email, FILTER_VALIDATE_EMAIL)) $errors[] = 'Valid email is required';
            if (empty($customer_group)) $errors[] = 'Customer group is required';
            if (empty($status)) $errors[] = 'Status is required';

            // Validate customer group
            $valid_groups = ['Retail', 'Wholesale', 'VIP'];
            if (!empty($customer_group) && !in_array($customer_group, $valid_groups)) {
                $errors[] = 'Invalid customer group';
            }

            // Validate status
            $valid_statuses = ['Active', 'Inactive', 'Blocked'];
            if (!empty($status) && !in_array($status, $valid_statuses)) {
                $errors[] = 'Invalid status';
            }

            if (!empty($errors)) {
                $message = implode(', ', $errors);
                $messageType = 'error';
            } else {
                // Check for duplicate email
                $check_stmt = $pdo->prepare("SELECT id FROM customers WHERE email = ?");
                $check_stmt->execute([$email]);
                if ($check_stmt->rowCount() > 0) {
                    $message = 'Email already exists!';
                    $messageType = 'error';
                } else {
                    // Insert new customer using prepared statement
                    $insert_stmt = $pdo->prepare("
                        INSERT INTO customers (customer_name, email, phone, address, customer_group, status, registration_date, created_at, updated_at)
                        VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW(), NOW())
                    ");

                    $result = $insert_stmt->execute([
                        $customer_name,
                        $email,
                        $phone,
                        $address,
                        $customer_group,
                        $status
                    ]);

                    if ($result) {
                        $message = 'Customer added successfully!';
                        $messageType = 'success';
                    } else {
                        $message = 'Error adding customer. Please try again.';
                        $messageType = 'error';
                    }
                }
            }
        }

    } catch (PDOException $e) {
        // Log the actual error for debugging (don't show to user)
        error_log("Database error: " . $e->getMessage());
        $message = 'A database error occurred. Please try again later.';
        $messageType = 'error';
    } catch (Exception $e) {
        // Log any other errors
        error_log("General error: " . $e->getMessage());
        $message = 'An error occurred. Please try again.';
        $messageType = 'error';
    }
}
?>

<?php include("include/header.php") ?>
<body class="bg-gray-100" x-data="{ sidebarOpen: false }">
    <div class="flex h-screen">
        <!-- Sidebar -->
        <?php include("include/sidebar.php") ?>

        <!-- Main Content -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Top Navigation -->
            <?php include("include/top_navbar.php") ?>

            <!-- Page Content -->
            <main class="flex-1 overflow-y-auto p-6">
                <!-- Page Header -->
                <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900">Customer Management</h1>
                        <p class="mt-1 text-sm text-gray-600">Manage your customers and customer relationships</p>
                    </div>
                    <div class="mt-4 md:mt-0 space-x-2">
                        <button onclick="openSimpleAddModal()" class="bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            <i class="fas fa-user-plus mr-2"></i> Add Customer
                        </button>
                    </div>
                </div>

                <!-- Success/Error Messages -->
                <?php if (!empty($message)): ?>
                <div class="mb-6">
                    <div class="<?php echo $messageType == 'success' ? 'bg-green-100 border border-green-400 text-green-700' : 'bg-red-100 border border-red-400 text-red-700'; ?> px-4 py-3 rounded relative" role="alert">
                        <strong class="font-bold"><?php echo $messageType == 'success' ? 'Success!' : 'Error!'; ?></strong>
                        <span class="block sm:inline"><?php echo htmlspecialchars($message); ?></span>
                        <span class="absolute top-0 bottom-0 right-0 px-4 py-3">
                            <svg class="fill-current h-6 w-6 text-<?php echo $messageType == 'success' ? 'green' : 'red'; ?>-500" role="button" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" onclick="this.parentElement.parentElement.style.display='none';">
                                <title>Close</title>
                                <path d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z"/>
                            </svg>
                        </span>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Customer Stats -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                    <?php
                    // Get real customer statistics from database
                    try {
                        $total_customers = $pdo->query("SELECT COUNT(*) as count FROM customers")->fetch()['count'] ?? 0;
                        $active_customers = $pdo->query("SELECT COUNT(*) as count FROM customers WHERE status = 'Active'")->fetch()['count'] ?? 0;
                        $new_this_month = $pdo->query("SELECT COUNT(*) as count FROM customers WHERE MONTH(registration_date) = MONTH(CURRENT_DATE()) AND YEAR(registration_date) = YEAR(CURRENT_DATE())")->fetch()['count'] ?? 0;
                        $vip_customers = $pdo->query("SELECT COUNT(*) as count FROM customers WHERE customer_group = 'VIP'")->fetch()['count'] ?? 0;
                    } catch (Exception $e) {
                        $total_customers = $active_customers = $new_this_month = $vip_customers = 0;
                    }
                    ?>
                    <div class="bg-white p-4 rounded-lg shadow">
                        <div class="flex items-center">
                            <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm text-gray-500">Total Customers</p>
                                <h3 class="text-xl font-semibold"><?php echo number_format($total_customers); ?></h3>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white p-4 rounded-lg shadow">
                        <div class="flex items-center">
                            <div class="p-3 rounded-full bg-green-100 text-green-600">
                                <i class="fas fa-user-check"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm text-gray-500">Active Customers</p>
                                <h3 class="text-xl font-semibold"><?php echo number_format($active_customers); ?></h3>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white p-4 rounded-lg shadow">
                        <div class="flex items-center">
                            <div class="p-3 rounded-full bg-yellow-100 text-yellow-600">
                                <i class="fas fa-user-clock"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm text-gray-500">New This Month</p>
                                <h3 class="text-xl font-semibold"><?php echo number_format($new_this_month); ?></h3>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white p-4 rounded-lg shadow">
                        <div class="flex items-center">
                            <div class="p-3 rounded-full bg-purple-100 text-purple-600">
                                <i class="fas fa-crown"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm text-gray-500">VIP Customers</p>
                                <h3 class="text-xl font-semibold"><?php echo number_format($vip_customers); ?></h3>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Filters -->
                <div class="bg-white rounded-lg shadow p-4 mb-6">
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                            <select class="w-full border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                                <option>All Status</option>
                                <option>Active</option>
                                <option>Inactive</option>
                                <option>Blocked</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Customer Group</label>
                            <select class="w-full border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                                <option>All Groups</option>
                                <option>Retail</option>
                                <option>Wholesale</option>
                                <option>VIP</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Registration Date</label>
                            <input type="date" class="w-full border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                        </div>
                        <div class="flex items-end">
                            <button class="w-full bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                Apply Filters
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Customers Table -->
                <div class="bg-white rounded-lg shadow overflow-hidden">
                    <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                        <h3 class="text-lg font-medium text-gray-900">All Customers</h3>
                        <div class="flex items-center space-x-2">
                            <button onclick="exportCustomers()" class="text-gray-500 hover:text-gray-700" title="Export Customers">
                                <i class="fas fa-file-export"></i>
                            </button>
                            <button onclick="printCustomers()" class="text-gray-500 hover:text-gray-700" title="Print Customers">
                                <i class="fas fa-print"></i>
                            </button>
                        </div>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Phone</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Group</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Registered</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <?php
                                try {
                                    // Fetch customers from database
                                    $customers_query = "SELECT id, customer_name, email, phone, address, customer_group, status, total_spent, registration_date FROM customers ORDER BY registration_date DESC LIMIT 10";
                                    $customers_result = $pdo->query($customers_query);
                                    $customers = $customers_result->fetchAll();

                                    if (count($customers) > 0) {
                                        foreach ($customers as $customer_item):
                                ?>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <div class="flex-shrink-0 h-10 w-10">
                                                <div class="h-10 w-10 rounded-full bg-indigo-100 flex items-center justify-center">
                                                    <span class="text-indigo-600 font-medium"><?php echo strtoupper(substr($customer_item['customer_name'], 0, 2)); ?></span>
                                                </div>
                                            </div>
                                            <div class="ml-4">
                                                <div class="text-sm font-medium text-gray-900"><?php echo htmlspecialchars($customer_item['customer_name']); ?></div>
                                                <div class="text-sm text-gray-500">ID: #<?php echo htmlspecialchars($customer_item['id']); ?></div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900"><?php echo htmlspecialchars($customer_item['email']); ?></div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900"><?php echo htmlspecialchars($customer_item['phone'] ?: 'N/A'); ?></div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <?php
                                        $group_colors = [
                                            'Retail' => 'bg-blue-100 text-blue-800',
                                            'Wholesale' => 'bg-green-100 text-green-800',
                                            'VIP' => 'bg-purple-100 text-purple-800'
                                        ];
                                        $group_color = $group_colors[$customer_item['customer_group']] ?? 'bg-gray-100 text-gray-800';
                                        ?>
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full <?php echo $group_color; ?>">
                                            <?php echo htmlspecialchars($customer_item['customer_group']); ?>
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900"><?php echo date('M d, Y', strtotime($customer_item['registration_date'])); ?></div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <?php
                                        $status_colors = [
                                            'Active' => 'bg-green-100 text-green-800',
                                            'Inactive' => 'bg-yellow-100 text-yellow-800',
                                            'Blocked' => 'bg-red-100 text-red-800'
                                        ];
                                        $status_color = $status_colors[$customer_item['status']] ?? 'bg-gray-100 text-gray-800';
                                        ?>
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full <?php echo $status_color; ?>">
                                            <?php echo htmlspecialchars($customer_item['status']); ?>
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                        <a href="#" onclick="viewSimpleCustomer(<?php echo $customer_item['id']; ?>)" class="text-blue-600 hover:text-blue-900 mr-3" title="View Customer">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="#" onclick="editCustomer(<?php echo $customer_item['id']; ?>)" class="text-indigo-600 hover:text-indigo-900 mr-3" title="Edit Customer">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="#" onclick="deleteCustomer(<?php echo $customer_item['id']; ?>)" class="text-red-600 hover:text-red-900" title="Delete Customer">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </td>
                                </tr>
                                <?php
                                        endforeach;
                                    } else {
                                ?>
                                <tr>
                                    <td colspan="7" class="px-6 py-4 text-center text-gray-500">
                                        <div class="flex flex-col items-center">
                                            <i class="fas fa-users text-4xl text-gray-300 mb-2"></i>
                                            <p class="text-lg font-medium">No customers found</p>
                                            <p class="text-sm">Click "Add Customer" to create your first customer</p>
                                        </div>
                                    </td>
                                </tr>
                                <?php
                                    }
                                } catch (Exception $e) {
                                    error_log("Error fetching customers: " . $e->getMessage());
                                ?>
                                <tr>
                                    <td colspan="7" class="px-6 py-4 text-center text-red-500">
                                        <div class="flex flex-col items-center">
                                            <i class="fas fa-exclamation-triangle text-4xl text-red-300 mb-2"></i>
                                            <p class="text-lg font-medium">Error loading customers</p>
                                            <p class="text-sm">Please try refreshing the page</p>
                                        </div>
                                    </td>
                                </tr>
                                <?php
                                }
                                ?>
                            </tbody>
                        </table>
                    </div>
                    <!-- Pagination -->
                    <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                        <div class="flex-1 flex justify-between sm:hidden">
                            <a href="#" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                Previous
                            </a>
                            <a href="#" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                Next
                            </a>
                        </div>
                        <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                            <div>
                                <p class="text-sm text-gray-700">
                                    <?php
                                    $displayed_count = count($customers ?? []);
                                    $total_count = $total_customers ?? 0;
                                    ?>
                                    Showing <span class="font-medium">1</span> to <span class="font-medium"><?php echo $displayed_count; ?></span> of <span class="font-medium"><?php echo $total_count; ?></span> results
                                </p>
                            </div>
                            <div>
                                <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                                    <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                        <span class="sr-only">Previous</span>
                                        <i class="fas fa-chevron-left h-5 w-5"></i>
                                    </a>
                                    <a href="#" aria-current="page" class="z-10 bg-indigo-50 border-indigo-500 text-indigo-600 relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                                        1
                                    </a>
                                    <a href="#" class="bg-white border-gray-300 text-gray-500 hover:bg-gray-50 relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                                        2
                                    </a>
                                    <a href="#" class="bg-white border-gray-300 text-gray-500 hover:bg-gray-50 relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                                        3
                                    </a>
                                    <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                        <span class="sr-only">Next</span>
                                        <i class="fas fa-chevron-right h-5 w-5"></i>
                                    </a>
                                </nav>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Add Customer Modal -->
    <div id="addCustomerModal" class="fixed inset-0 z-50 overflow-y-auto hidden" aria-labelledby="modal-title" role="dialog" aria-modal="true">
        <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>
            <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

            <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full">
                <form method="POST" action="">
                    <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                        <div class="sm:flex sm:items-start">
                            <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                                <div class="flex justify-between items-center">
                                    <h3 class="text-lg leading-6 font-medium text-gray-900" id="modal-title">
                                        Add New Customer
                                    </h3>
                                    <button type="button" onclick="closeAddModal()" class="text-gray-400 hover:text-gray-500 focus:outline-none">
                                        <i class="fas fa-times text-xl"></i>
                                    </button>
                                </div>

                                <div class="mt-5 space-y-6">
                                    <div class="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                                        <!-- Customer Name -->
                                        <div class="sm:col-span-4">
                                            <label for="customer_name" class="block text-sm font-medium text-gray-700">Customer Name *</label>
                                            <div class="mt-1">
                                                <input type="text" name="customer_name" id="customer_name" required class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md p-2 border">
                                            </div>
                                        </div>

                                        <!-- Email -->
                                        <div class="sm:col-span-2">
                                            <label for="email" class="block text-sm font-medium text-gray-700">Email *</label>
                                            <div class="mt-1">
                                                <input type="email" name="email" id="email" required class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md p-2 border">
                                            </div>
                                        </div>

                                        <!-- Phone -->
                                        <div class="sm:col-span-3">
                                            <label for="phone" class="block text-sm font-medium text-gray-700">Phone</label>
                                            <div class="mt-1">
                                                <input type="tel" name="phone" id="phone" class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md p-2 border">
                                            </div>
                                        </div>

                                        <!-- Customer Group -->
                                        <div class="sm:col-span-3">
                                            <label for="customer_group" class="block text-sm font-medium text-gray-700">Customer Group *</label>
                                            <select id="customer_group" name="customer_group" required class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md border">
                                                <option value="">Select group</option>
                                                <option value="Retail">Retail</option>
                                                <option value="Wholesale">Wholesale</option>
                                                <option value="VIP">VIP</option>
                                            </select>
                                        </div>

                                        <!-- Status -->
                                        <div class="sm:col-span-6">
                                            <label for="status" class="block text-sm font-medium text-gray-700">Status *</label>
                                            <select id="status" name="status" required class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md border">
                                                <option value="">Select status</option>
                                                <option value="Active">Active</option>
                                                <option value="Inactive">Inactive</option>
                                                <option value="Blocked">Blocked</option>
                                            </select>
                                        </div>

                                        <!-- Address -->
                                        <div class="sm:col-span-6">
                                            <label for="address" class="block text-sm font-medium text-gray-700">Address</label>
                                            <div class="mt-1">
                                                <textarea id="address" name="address" rows="3" class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border border-gray-300 rounded-md p-2" placeholder="Customer address..."></textarea>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                        <button type="submit" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-indigo-600 text-base font-medium text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:ml-3 sm:w-auto sm:text-sm">
                            Add Customer
                        </button>
                        <button type="button" onclick="closeAddModal()" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                            Cancel
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Simple Modal for Add Customer -->
    <div id="simpleAddModal" class="modal-simple">
        <div class="modal-content-simple">
            <div class="p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-medium text-gray-900">Add New Customer</h3>
                    <button type="button" onclick="closeSimpleAddModal()" class="text-gray-400 hover:text-gray-500">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>

                <form method="POST" action="">
                    <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
                        <!-- Customer Name -->
                        <div class="sm:col-span-2">
                            <label for="simple_customer_name" class="block text-sm font-medium text-gray-700">Customer Name *</label>
                            <input type="text" name="customer_name" id="simple_customer_name" required
                                   class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 p-2 border">
                        </div>

                        <!-- Email -->
                        <div>
                            <label for="simple_email" class="block text-sm font-medium text-gray-700">Email *</label>
                            <input type="email" name="email" id="simple_email" required
                                   class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 p-2 border">
                        </div>

                        <!-- Phone -->
                        <div>
                            <label for="simple_phone" class="block text-sm font-medium text-gray-700">Phone</label>
                            <input type="tel" name="phone" id="simple_phone"
                                   class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 p-2 border">
                        </div>

                        <!-- Customer Group -->
                        <div>
                            <label for="simple_customer_group" class="block text-sm font-medium text-gray-700">Customer Group *</label>
                            <select id="simple_customer_group" name="customer_group" required
                                    class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 p-2 border">
                                <option value="">Select group</option>
                                <option value="Retail">Retail</option>
                                <option value="Wholesale">Wholesale</option>
                                <option value="VIP">VIP</option>
                            </select>
                        </div>

                        <!-- Status -->
                        <div>
                            <label for="simple_status" class="block text-sm font-medium text-gray-700">Status *</label>
                            <select id="simple_status" name="status" required
                                    class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 p-2 border">
                                <option value="">Select status</option>
                                <option value="Active">Active</option>
                                <option value="Inactive">Inactive</option>
                                <option value="Blocked">Blocked</option>
                            </select>
                        </div>

                        <!-- Address -->
                        <div class="sm:col-span-2">
                            <label for="simple_address" class="block text-sm font-medium text-gray-700">Address</label>
                            <textarea id="simple_address" name="address" rows="3"
                                      class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 p-2 border"
                                      placeholder="Customer address..."></textarea>
                        </div>
                    </div>

                    <div class="mt-6 flex justify-end space-x-3">
                        <button type="button" onclick="closeSimpleAddModal()"
                                class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            Cancel
                        </button>
                        <button type="submit"
                                class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            Add Customer
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- CSS for Simple Modal -->
    <style>
        .modal-simple {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            backdrop-filter: blur(4px);
        }

        .modal-content-simple {
            background-color: #fefefe;
            margin: 5% auto;
            border-radius: 8px;
            width: 90%;
            max-width: 600px;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            animation: modalSlideIn 0.3s ease-out;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .modal-simple.show {
            display: block;
        }
    </style>

    <!-- JavaScript Functions -->
    <script>
        // Simple Modal Functions
        function openSimpleAddModal() {
            document.getElementById('simpleAddModal').classList.add('show');
            document.body.style.overflow = 'hidden';
        }

        function closeSimpleAddModal() {
            document.getElementById('simpleAddModal').classList.remove('show');
            document.body.style.overflow = 'auto';
            // Reset form
            document.getElementById('simpleAddModal').querySelector('form').reset();
        }

        // Customer Management Functions
        function viewSimpleCustomer(customerId) {
            // For now, just show an alert. You can implement a proper view modal later
            alert('View customer details for ID: ' + customerId);
        }

        function editCustomer(customerId) {
            // For now, just show an alert. You can implement edit functionality later
            alert('Edit customer with ID: ' + customerId);
        }

        function deleteCustomer(customerId) {
            if (confirm('Are you sure you want to delete this customer?')) {
                // Create a form to submit the delete request
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = '';

                const actionInput = document.createElement('input');
                actionInput.type = 'hidden';
                actionInput.name = 'action';
                actionInput.value = 'delete';

                const idInput = document.createElement('input');
                idInput.type = 'hidden';
                idInput.name = 'id';
                idInput.value = customerId;

                form.appendChild(actionInput);
                form.appendChild(idInput);
                document.body.appendChild(form);
                form.submit();
            }
        }

        // Export and Print Functions
        function exportCustomers() {
            alert('Export functionality will be implemented soon.');
        }

        function printCustomers() {
            window.print();
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            const modal = document.getElementById('simpleAddModal');
            if (event.target === modal) {
                closeSimpleAddModal();
            }
        }

        // Close modal with Escape key
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                closeSimpleAddModal();
            }
        });
    </script>
</body>
</html>