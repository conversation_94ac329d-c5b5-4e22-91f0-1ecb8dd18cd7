<?php
require_once 'database/conn.php';
require_once 'check_session.php';

// Require user to be logged in
requireLogin();

// Initialize variables
$message = '';
$messageType = '';
$search = $_GET['search'] ?? '';
$status_filter = $_GET['status'] ?? '';
$date_from = $_GET['date_from'] ?? '';
$date_to = $_GET['date_to'] ?? '';
$payment_method = $_GET['payment_method'] ?? '';
$page = max(1, intval($_GET['page'] ?? 1));
$limit = 10;
$offset = ($page - 1) * $limit;

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'add_payment':
                // Handle add new payment
                $customer_id = filter_var($_POST['customer_id'] ?? 0, FILTER_VALIDATE_INT);
                $order_id = filter_var($_POST['order_id'] ?? null, FILTER_VALIDATE_INT);
                $amount = filter_var($_POST['amount'] ?? 0.0, FILTER_VALIDATE_FLOAT);
                $payment_method = trim($_POST['payment_method'] ?? '');
                $status = trim($_POST['status'] ?? 'pending');
                $reference_number = trim($_POST['reference_number'] ?? '');
                $notes = trim($_POST['notes'] ?? '');

                // Server-side validation
                $errors = [];
                if ($customer_id === false || $customer_id <= 0) $errors[] = 'Valid customer is required';
                if ($amount === false || $amount <= 0) $errors[] = 'Valid amount is required';
                if (empty($payment_method)) $errors[] = 'Payment method is required';
                if (empty($status)) $errors[] = 'Status is required';

                if (!empty($errors)) {
                    $message = implode(', ', $errors);
                    $messageType = 'error';
                } else {
                    try {
                        // Generate unique transaction ID
                        $transaction_id = 'TXN-' . str_pad(mt_rand(1, 99999), 5, '0', STR_PAD_LEFT);

                        // Check if transaction ID already exists
                        $check_stmt = $pdo->prepare("SELECT id FROM payments WHERE transaction_id = ?");
                        $check_stmt->execute([$transaction_id]);
                        while ($check_stmt->rowCount() > 0) {
                            $transaction_id = 'TXN-' . str_pad(mt_rand(1, 99999), 5, '0', STR_PAD_LEFT);
                            $check_stmt->execute([$transaction_id]);
                        }

                        // Insert new payment
                        $insert_stmt = $pdo->prepare("
                            INSERT INTO payments (transaction_id, order_id, customer_id, amount, payment_method, status, reference_number, notes, payment_date, created_at, updated_at)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW(), NOW())
                        ");

                        $result = $insert_stmt->execute([
                            $transaction_id,
                            $order_id ?: null,
                            $customer_id,
                            $amount,
                            $payment_method,
                            $status,
                            $reference_number,
                            $notes
                        ]);

                        if ($result) {
                            $message = 'Payment added successfully!';
                            $messageType = 'success';
                        } else {
                            $message = 'Failed to add payment. Please try again.';
                            $messageType = 'error';
                        }
                    } catch (PDOException $e) {
                        error_log("Payment insert error: " . $e->getMessage());
                        $message = 'Database error occurred. Please try again.';
                        $messageType = 'error';
                    }
                }
                break;

            case 'edit_payment':
                // Handle edit payment
                $payment_id = filter_var($_POST['payment_id'] ?? 0, FILTER_VALIDATE_INT);
                $customer_id = filter_var($_POST['customer_id'] ?? 0, FILTER_VALIDATE_INT);
                $order_id = filter_var($_POST['order_id'] ?? null, FILTER_VALIDATE_INT);
                $amount = filter_var($_POST['amount'] ?? 0.0, FILTER_VALIDATE_FLOAT);
                $payment_method = trim($_POST['payment_method'] ?? '');
                $status = trim($_POST['status'] ?? '');
                $reference_number = trim($_POST['reference_number'] ?? '');
                $notes = trim($_POST['notes'] ?? '');

                // Server-side validation
                $errors = [];
                if ($payment_id === false || $payment_id <= 0) $errors[] = 'Invalid payment ID';
                if ($customer_id === false || $customer_id <= 0) $errors[] = 'Valid customer is required';
                if ($amount === false || $amount <= 0) $errors[] = 'Valid amount is required';
                if (empty($payment_method)) $errors[] = 'Payment method is required';
                if (empty($status)) $errors[] = 'Status is required';

                if (!empty($errors)) {
                    $message = implode(', ', $errors);
                    $messageType = 'error';
                } else {
                    try {
                        // Check if payment exists
                        $check_stmt = $pdo->prepare("SELECT id FROM payments WHERE id = ?");
                        $check_stmt->execute([$payment_id]);
                        if (!$check_stmt->fetch()) {
                            $message = 'Payment not found.';
                            $messageType = 'error';
                        } else {
                            // Update payment
                            $update_stmt = $pdo->prepare("
                                UPDATE payments SET
                                    customer_id = ?,
                                    order_id = ?,
                                    amount = ?,
                                    payment_method = ?,
                                    status = ?,
                                    reference_number = ?,
                                    notes = ?,
                                    updated_at = NOW()
                                WHERE id = ?
                            ");

                            $result = $update_stmt->execute([
                                $customer_id,
                                $order_id ?: null,
                                $amount,
                                $payment_method,
                                $status,
                                $reference_number,
                                $notes,
                                $payment_id
                            ]);

                            if ($result) {
                                $message = 'Payment updated successfully!';
                                $messageType = 'success';
                            } else {
                                $message = 'Failed to update payment. Please try again.';
                                $messageType = 'error';
                            }
                        }
                    } catch (PDOException $e) {
                        error_log("Payment update error: " . $e->getMessage());
                        $message = 'Database error occurred. Please try again.';
                        $messageType = 'error';
                    }
                }
                break;

            case 'delete_payment':
                // Handle delete payment
                $payment_id = filter_var($_POST['payment_id'] ?? 0, FILTER_VALIDATE_INT);

                if ($payment_id === false || $payment_id <= 0) {
                    $message = 'Invalid payment ID.';
                    $messageType = 'error';
                } else {
                    try {
                        // Check if payment exists
                        $check_stmt = $pdo->prepare("SELECT transaction_id FROM payments WHERE id = ?");
                        $check_stmt->execute([$payment_id]);
                        $payment = $check_stmt->fetch();

                        if (!$payment) {
                            $message = 'Payment not found.';
                            $messageType = 'error';
                        } else {
                            // Delete the payment
                            $delete_stmt = $pdo->prepare("DELETE FROM payments WHERE id = ?");
                            $result = $delete_stmt->execute([$payment_id]);

                            if ($result) {
                                $message = 'Payment deleted successfully!';
                                $messageType = 'success';
                            } else {
                                $message = 'Failed to delete payment. Please try again.';
                                $messageType = 'error';
                            }
                        }
                    } catch (PDOException $e) {
                        error_log("Payment delete error: " . $e->getMessage());
                        $message = 'Database error occurred. Please try again.';
                        $messageType = 'error';
                    }
                }
                break;
        }
    }
}

// Handle AJAX requests for payment data
if (isset($_GET['action']) && $_GET['action'] === 'get_payment' && isset($_GET['id'])) {
    header('Content-Type: application/json');

    try {
        $payment_id = intval($_GET['id']);
        $stmt = $pdo->prepare("
            SELECT
                p.id,
                p.transaction_id,
                p.customer_id,
                p.order_id,
                p.amount,
                p.payment_method,
                p.status,
                p.reference_number,
                p.notes,
                p.payment_date,
                u.name as customer_name,
                u.email as customer_email,
                o.order_number
            FROM payments p
            LEFT JOIN users u ON p.customer_id = u.id
            LEFT JOIN orders o ON p.order_id = o.id
            WHERE p.id = ?
        ");
        $stmt->execute([$payment_id]);
        $payment = $stmt->fetch();

        if ($payment) {
            // Format dates for display
            $payment['payment_date_formatted'] = date('Y-m-d', strtotime($payment['payment_date']));

            echo json_encode(['success' => true, 'payment' => $payment]);
        } else {
            echo json_encode(['success' => false, 'message' => 'Payment not found']);
        }
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => 'Error fetching payment data']);
    }
    exit;
}

// Build WHERE clause for filtering
$where_conditions = [];
$params = [];

if (!empty($search)) {
    $where_conditions[] = "(p.transaction_id LIKE ? OR u.name LIKE ? OR u.email LIKE ?)";
    $params[] = "%$search%";
    $params[] = "%$search%";
    $params[] = "%$search%";
}

if (!empty($status_filter)) {
    $where_conditions[] = "p.status = ?";
    $params[] = $status_filter;
}

if (!empty($date_from)) {
    $where_conditions[] = "DATE(p.payment_date) >= ?";
    $params[] = $date_from;
}

if (!empty($date_to)) {
    $where_conditions[] = "DATE(p.payment_date) <= ?";
    $params[] = $date_to;
}

if (!empty($payment_method)) {
    $where_conditions[] = "p.payment_method = ?";
    $params[] = $payment_method;
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// Get payment statistics
try {
    $stats_query = "
        SELECT
            COUNT(*) as total_payments,
            SUM(CASE WHEN status = 'completed' THEN amount ELSE 0 END) as total_revenue,
            COUNT(CASE WHEN status = 'completed' THEN 1 END) as successful_payments,
            COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_payments,
            COUNT(CASE WHEN status = 'refunded' THEN 1 END) as refunded_payments
        FROM payments p
    ";
    $stats_result = $pdo->query($stats_query);
    $stats = $stats_result->fetch();
} catch (PDOException $e) {
    $stats = [
        'total_payments' => 0,
        'total_revenue' => 0,
        'successful_payments' => 0,
        'failed_payments' => 0,
        'refunded_payments' => 0
    ];
}

// Get total count for pagination
try {
    $count_query = "
        SELECT COUNT(*) as total
        FROM payments p
        LEFT JOIN users u ON p.customer_id = u.id
        $where_clause
    ";
    $count_stmt = $pdo->prepare($count_query);
    $count_stmt->execute($params);
    $total_records = $count_stmt->fetch()['total'];
    $total_pages = ceil($total_records / $limit);
} catch (PDOException $e) {
    $total_records = 0;
    $total_pages = 1;
}

// Get customers for dropdown
try {
    $customers_query = "SELECT id, name, email FROM users WHERE user_type = 'Customer' ORDER BY name";
    $customers_result = $pdo->query($customers_query);
    $customers = $customers_result->fetchAll();
} catch (PDOException $e) {
    $customers = [];
}

// Get orders for dropdown
try {
    $orders_query = "SELECT id, order_number, total_amount FROM orders ORDER BY order_date DESC LIMIT 50";
    $orders_result = $pdo->query($orders_query);
    $orders = $orders_result->fetchAll();
} catch (PDOException $e) {
    $orders = [];
}

// Get payments data
try {
    $payments_query = "
        SELECT
            p.id,
            p.transaction_id,
            p.amount,
            p.payment_method,
            p.status,
            p.payment_date,
            p.reference_number,
            p.notes,
            p.created_at,
            u.name as customer_name,
            u.email as customer_email,
            u.phone as customer_phone,
            o.order_number,
            o.total_amount as order_total
        FROM payments p
        LEFT JOIN users u ON p.customer_id = u.id
        LEFT JOIN orders o ON p.order_id = o.id
        $where_clause
        ORDER BY p.payment_date DESC
        LIMIT $limit OFFSET $offset
    ";
    $payments_stmt = $pdo->prepare($payments_query);
    $payments_stmt->execute($params);
    $payments = $payments_stmt->fetchAll();
} catch (PDOException $e) {
    $payments = [];
    $message = "Error loading payments data.";
    $messageType = 'error';
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payments - Meat Management System</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
    <link rel="stylesheet" href="css/styles.css">
</head>
<body class="bg-gray-100" x-data="{ sidebarOpen: false, showAddPaymentModal: false }">
    <div class="flex h-screen">
        <!-- Sidebar -->
        <div :class="{'translate-x-0 ease-out': sidebarOpen, '-translate-x-full ease-in': !sidebarOpen}" class="fixed inset-y-0 left-0 z-30 w-64 bg-gray-800 text-white transform transition duration-300 lg:translate-x-0 lg:static lg:inset-0">
            <div class="p-4 border-b border-gray-700 flex justify-between items-center lg:justify-start">
                <div>
                    <h1 class="text-2xl font-bold">MeatMS</h1>
                    <p class="text-sm text-gray-400">Admin Panel</p>
                </div>
                <button @click="sidebarOpen = false" class="text-gray-400 hover:text-white lg:hidden">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
            <nav class="mt-4">
                <div class="px-4 py-2 text-gray-400 uppercase text-xs font-semibold">Main</div>
                <a href="index.php" class="block px-4 py-2 text-gray-300 hover:bg-gray-700 hover:text-white rounded mx-2">
                    <i class="fas fa-tachometer-alt mr-2"></i> Dashboard
                </a>

                <div class="px-4 py-2 text-gray-400 uppercase text-xs font-semibold mt-4">Management</div>
                <a href="products.php" class="block px-4 py-2 text-gray-300 hover:bg-gray-700 hover:text-white rounded mx-2">
                    <i class="fas fa-box mr-2"></i> Products
                </a>
                <a href="orders.php" class="block px-4 py-2 text-gray-300 hover:bg-gray-700 hover:text-white rounded mx-2">
                    <i class="fas fa-shopping-cart mr-2"></i> Orders
                </a>
                <a href="users.php" class="block px-4 py-2 text-gray-300 hover:bg-gray-700 hover:text-white rounded mx-2">
                    <i class="fas fa-users mr-2"></i> Users
                </a>
                <a href="vendors.php" class="block px-4 py-2 text-gray-300 hover:bg-gray-700 hover:text-white rounded mx-2">
                    <i class="fas fa-store mr-2"></i> Vendors
                </a>
                <a href="customers.php" class="block px-4 py-2 text-gray-300 hover:bg-gray-700 hover:text-white rounded mx-2">
                    <i class="fas fa-address-book mr-2"></i> Customers
                </a>

                <div class="px-4 py-2 text-gray-400 uppercase text-xs font-semibold mt-4">Sales & Reports</div>
                <a href="sales.php" class="block px-4 py-2 text-gray-300 hover:bg-gray-700 hover:text-white rounded mx-2">
                    <i class="fas fa-chart-line mr-2"></i> Sales
                </a>
                <a href="reports.php" class="block px-4 py-2 text-gray-300 hover:bg-gray-700 hover:text-white rounded mx-2">
                    <i class="fas fa-file-alt mr-2"></i> Reports
                </a>

                <div class="px-4 py-2 text-gray-400 uppercase text-xs font-semibold mt-4">Financial</div>
                <a href="payments.php" class="block px-4 py-2 bg-indigo-700 text-white rounded mx-2"> <!-- Active link -->
                    <i class="fas fa-credit-card mr-2"></i> Payments
                </a>
            </nav>
        </div>

        <!-- Overlay for mobile sidebar -->
        <div x-show="sidebarOpen" @click="sidebarOpen = false" class="fixed inset-0 z-20 bg-black opacity-50 transition-opacity lg:hidden" x-cloak></div>

        <!-- Main Content -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Top Navigation -->
            <header class="bg-white shadow">
                <div class="flex items-center justify-between px-6 py-4">
                    <div class="flex items-center">
                        <button @click="sidebarOpen = !sidebarOpen" class="text-gray-500 focus:outline-none lg:hidden">
                            <i class="fas fa-bars text-xl"></i>
                        </button>
                        <h2 class="text-xl font-semibold text-gray-800 ml-2">Payments</h2>
                    </div>
                    <div class="flex items-center space-x-4">
                        <form method="GET" class="relative">
                            <input type="text" name="search" value="<?= htmlspecialchars($search) ?>" placeholder="Search transactions..." class="pl-10 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500">
                            <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>
                            <?php if (!empty($status_filter)): ?>
                                <input type="hidden" name="status" value="<?= htmlspecialchars($status_filter) ?>">
                            <?php endif; ?>
                            <?php if (!empty($date_from)): ?>
                                <input type="hidden" name="date_from" value="<?= htmlspecialchars($date_from) ?>">
                            <?php endif; ?>
                            <?php if (!empty($date_to)): ?>
                                <input type="hidden" name="date_to" value="<?= htmlspecialchars($date_to) ?>">
                            <?php endif; ?>
                            <?php if (!empty($payment_method)): ?>
                                <input type="hidden" name="payment_method" value="<?= htmlspecialchars($payment_method) ?>">
                            <?php endif; ?>
                        </form>
                        <button class="p-2 text-gray-600 hover:text-gray-900">
                            <i class="fas fa-bell"></i>
                        </button>
                        <div class="relative" x-data="{ dropdownOpen: false }">
                            <button @click="dropdownOpen = !dropdownOpen" class="flex items-center space-x-2 focus:outline-none">
                                <img class="w-8 h-8 rounded-full" src="https://via.placeholder.com/32" alt="User">
                                <span class="text-gray-700">Admin</span>
                                <i class="fas fa-chevron-down text-xs"></i>
                            </button>
                            <div x-show="dropdownOpen" @click.away="dropdownOpen = false" class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-20" x-cloak>
                                <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Profile</a>
                                <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Settings</a>
                                <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Logout</a>
                            </div>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Page Content -->
            <main class="flex-1 overflow-y-auto p-6">
                <!-- Message Display -->
                <?php if (!empty($message)): ?>
                    <div class="mb-4 p-4 rounded-lg <?= $messageType === 'error' ? 'bg-red-100 border border-red-400 text-red-700' : 'bg-green-100 border border-green-400 text-green-700' ?>">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <i class="fas <?= $messageType === 'error' ? 'fa-exclamation-circle' : 'fa-check-circle' ?> h-5 w-5"></i>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm font-medium"><?= htmlspecialchars($message) ?></p>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Page Header -->
                <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900">Payment Management</h1>
                        <p class="mt-1 text-sm text-gray-600">Track and manage all transactions</p>
                    </div>
                    <div class="mt-4 md:mt-0 flex space-x-3">
                        <a href="export_payments.php" class="bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500">
                            <i class="fas fa-download mr-2"></i> Export
                        </a>
                        <button onclick="openAddPaymentModal()" class="bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            <i class="fas fa-plus mr-2"></i> Add Payment
                        </button>
                    </div>
                </div>

                <!-- Payment Stats -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                    <div class="bg-white p-6 rounded-lg shadow">
                        <div class="flex items-center">
                            <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                                <i class="fas fa-dollar-sign"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm text-gray-500">Total Revenue</p>
                                <h3 class="text-xl font-semibold">$<?= number_format($stats['total_revenue'] ?? 0, 2) ?></h3>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white p-6 rounded-lg shadow">
                        <div class="flex items-center">
                            <div class="p-3 rounded-full bg-green-100 text-green-600">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm text-gray-500">Successful Payments</p>
                                <h3 class="text-xl font-semibold"><?= number_format($stats['successful_payments'] ?? 0) ?></h3>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white p-6 rounded-lg shadow">
                        <div class="flex items-center">
                            <div class="p-3 rounded-full bg-red-100 text-red-600">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm text-gray-500">Failed Payments</p>
                                <h3 class="text-xl font-semibold"><?= number_format($stats['failed_payments'] ?? 0) ?></h3>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white p-6 rounded-lg shadow">
                        <div class="flex items-center">
                            <div class="p-3 rounded-full bg-yellow-100 text-yellow-600">
                                <i class="fas fa-undo-alt"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm text-gray-500">Refunds Processed</p>
                                <h3 class="text-xl font-semibold"><?= number_format($stats['refunded_payments'] ?? 0) ?></h3>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Filters -->
                <div class="bg-white rounded-lg shadow p-4 mb-6">
                    <form method="GET" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                        <?php if (!empty($search)): ?>
                            <input type="hidden" name="search" value="<?= htmlspecialchars($search) ?>">
                        <?php endif; ?>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Payment Status</label>
                            <select name="status" class="w-full border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                                <option value="">All Status</option>
                                <option value="completed" <?= $status_filter === 'completed' ? 'selected' : '' ?>>Completed</option>
                                <option value="pending" <?= $status_filter === 'pending' ? 'selected' : '' ?>>Pending</option>
                                <option value="failed" <?= $status_filter === 'failed' ? 'selected' : '' ?>>Failed</option>
                                <option value="refunded" <?= $status_filter === 'refunded' ? 'selected' : '' ?>>Refunded</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Date Range (From)</label>
                            <input type="date" name="date_from" value="<?= htmlspecialchars($date_from) ?>" class="w-full border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Date Range (To)</label>
                            <input type="date" name="date_to" value="<?= htmlspecialchars($date_to) ?>" class="w-full border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Payment Method</label>
                            <select name="payment_method" class="w-full border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                                <option value="">All Methods</option>
                                <option value="shilling_somali" <?= $payment_method === 'shilling_somali' ? 'selected' : '' ?>>Shilling Somali</option>
                                <option value="evc" <?= $payment_method === 'evc' ? 'selected' : '' ?>>EVC</option>
                                <option value="bank_transfer" <?= $payment_method === 'bank_transfer' ? 'selected' : '' ?>>Bank Transfer</option>
                                <option value="cash_dollar" <?= $payment_method === 'cash_dollar' ? 'selected' : '' ?>>Cash Dollar</option>
                            </select>
                        </div>
                        <div class="flex items-end space-x-2">
                            <button type="submit" class="flex-1 bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                Apply Filters
                            </button>
                            <a href="payments.php" class="bg-gray-300 text-gray-700 px-3 py-2 rounded-lg hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500" title="Clear Filters">
                                <i class="fas fa-times"></i>
                            </a>
                        </div>
                    </form>
                </div>

                <!-- Payments Table -->
                <div class="bg-white rounded-lg shadow overflow-hidden">
                    <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                        <h3 class="text-lg font-medium text-gray-900">Transaction History</h3>
                         <div class="flex items-center space-x-2">
                            <button class="text-gray-500 hover:text-gray-700" title="Download CSV">
                                <i class="fas fa-download"></i>
                            </button>
                        </div>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Transaction ID</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Method</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <?php if (empty($payments)): ?>
                                    <tr>
                                        <td colspan="7" class="px-6 py-4 text-center text-gray-500">
                                            <div class="flex flex-col items-center">
                                                <i class="fas fa-credit-card text-4xl text-gray-300 mb-2"></i>
                                                <p>No payment records found.</p>
                                                <?php if (!empty($search) || !empty($status_filter) || !empty($date_from) || !empty($date_to) || !empty($payment_method)): ?>
                                                    <p class="text-sm mt-1">Try adjusting your filters.</p>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                <?php else: ?>
                                    <?php foreach ($payments as $payment): ?>
                                        <?php
                                        // Determine status styling
                                        $status_classes = [
                                            'completed' => 'bg-green-100 text-green-800',
                                            'pending' => 'bg-yellow-100 text-yellow-800',
                                            'failed' => 'bg-red-100 text-red-800',
                                            'refunded' => 'bg-gray-100 text-gray-800'
                                        ];
                                        $status_class = $status_classes[$payment['status']] ?? 'bg-gray-100 text-gray-800';

                                        // Format payment method display
                                        $method_display = [
                                            'shilling_somali' => 'Shilling Somali',
                                            'evc' => 'EVC',
                                            'bank_transfer' => 'Bank Transfer',
                                            'cash_dollar' => 'Cash Dollar'
                                        ];
                                        $method_text = $method_display[$payment['payment_method']] ?? ucfirst(str_replace('_', ' ', $payment['payment_method']));
                                        ?>
                                        <tr class="hover:bg-gray-50">
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                <?= htmlspecialchars($payment['transaction_id']) ?>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                <?= date('Y-m-d', strtotime($payment['payment_date'])) ?>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="text-sm text-gray-900"><?= htmlspecialchars($payment['customer_name'] ?? 'N/A') ?></div>
                                                <div class="text-sm text-gray-500"><?= htmlspecialchars($payment['customer_email'] ?? 'N/A') ?></div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                $<?= number_format($payment['amount'], 2) ?>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                <?= htmlspecialchars($method_text) ?>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full <?= $status_class ?>">
                                                    <?= ucfirst($payment['status']) ?>
                                                </span>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                                <a href="#" onclick="viewPaymentDetails(<?= $payment['id'] ?>)" class="text-indigo-600 hover:text-indigo-900 mr-3" title="View Details">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="#" onclick="editPayment(<?= $payment['id'] ?>)" class="text-green-600 hover:text-green-900 mr-3" title="Edit Payment">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <?php if ($payment['status'] === 'failed'): ?>
                                                    <a href="#" onclick="retryPayment(<?= $payment['id'] ?>)" class="text-yellow-600 hover:text-yellow-900 mr-3" title="Retry Payment">
                                                        <i class="fas fa-redo"></i>
                                                    </a>
                                                <?php endif; ?>
                                                <a href="#" onclick="deletePayment(<?= $payment['id'] ?>, '<?= htmlspecialchars($payment['transaction_id']) ?>')" class="text-red-600 hover:text-red-900" title="Delete Payment">
                                                    <i class="fas fa-trash"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                    <!-- Pagination -->
                    <?php if ($total_records > 0): ?>
                        <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                            <div class="flex-1 flex justify-between sm:hidden">
                                <?php if ($page > 1): ?>
                                    <a href="?<?= http_build_query(array_merge($_GET, ['page' => $page - 1])) ?>" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">Previous</a>
                                <?php endif; ?>
                                <?php if ($page < $total_pages): ?>
                                    <a href="?<?= http_build_query(array_merge($_GET, ['page' => $page + 1])) ?>" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">Next</a>
                                <?php endif; ?>
                            </div>
                            <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                                <div>
                                    <p class="text-sm text-gray-700">
                                        Showing <span class="font-medium"><?= ($page - 1) * $limit + 1 ?></span> to <span class="font-medium"><?= min($page * $limit, $total_records) ?></span> of <span class="font-medium"><?= $total_records ?></span> results
                                    </p>
                                </div>
                                <div>
                                    <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                                        <?php if ($page > 1): ?>
                                            <a href="?<?= http_build_query(array_merge($_GET, ['page' => $page - 1])) ?>" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                                <span class="sr-only">Previous</span>
                                                <i class="fas fa-chevron-left h-5 w-5"></i>
                                            </a>
                                        <?php endif; ?>

                                        <?php
                                        $start_page = max(1, $page - 2);
                                        $end_page = min($total_pages, $page + 2);

                                        for ($i = $start_page; $i <= $end_page; $i++):
                                        ?>
                                            <a href="?<?= http_build_query(array_merge($_GET, ['page' => $i])) ?>"
                                               class="<?= $i === $page ? 'z-10 bg-indigo-50 border-indigo-500 text-indigo-600' : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50' ?> relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                                                <?= $i ?>
                                            </a>
                                        <?php endfor; ?>

                                        <?php if ($page < $total_pages): ?>
                                            <a href="?<?= http_build_query(array_merge($_GET, ['page' => $page + 1])) ?>" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                                <span class="sr-only">Next</span>
                                                <i class="fas fa-chevron-right h-5 w-5"></i>
                                            </a>
                                        <?php endif; ?>
                                    </nav>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </main>
        </div>
    </div>

    <!-- Add Payment Modal -->
    <div id="addPaymentModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
        <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">Add New Payment</h3>
                    <button onclick="closeAddPaymentModal()" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>

                <form method="POST" class="space-y-4">
                    <input type="hidden" name="action" value="add_payment">

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <!-- Customer Selection -->
                        <div>
                            <label for="customer_id" class="block text-sm font-medium text-gray-700 mb-1">Customer *</label>
                            <select name="customer_id" id="customer_id" required class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                                <option value="">Select Customer</option>
                                <?php foreach ($customers as $customer): ?>
                                    <option value="<?= $customer['id'] ?>"><?= htmlspecialchars($customer['name']) ?> (<?= htmlspecialchars($customer['email']) ?>)</option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <!-- Order Selection (Optional) -->
                        <div>
                            <label for="order_id" class="block text-sm font-medium text-gray-700 mb-1">Order (Optional)</label>
                            <select name="order_id" id="order_id" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                                <option value="">No specific order</option>
                                <?php foreach ($orders as $order): ?>
                                    <option value="<?= $order['id'] ?>"><?= htmlspecialchars($order['order_number']) ?> ($<?= number_format($order['total_amount'], 2) ?>)</option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <!-- Amount -->
                        <div>
                            <label for="amount" class="block text-sm font-medium text-gray-700 mb-1">Amount *</label>
                            <input type="number" name="amount" id="amount" step="0.01" min="0.01" required
                                   class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                                   placeholder="0.00">
                        </div>

                        <!-- Payment Method -->
                        <div>
                            <label for="payment_method" class="block text-sm font-medium text-gray-700 mb-1">Payment Method *</label>
                            <select name="payment_method" id="payment_method" required class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                                <option value="">Select Payment Method</option>
                                <option value="shilling_somali">Shilling Somali</option>
                                <option value="evc">EVC</option>
                                <option value="bank_transfer">Bank Transfer</option>
                                <option value="cash_dollar">Cash Dollar</option>
                            </select>
                        </div>

                        <!-- Status -->
                        <div>
                            <label for="status" class="block text-sm font-medium text-gray-700 mb-1">Status *</label>
                            <select name="status" id="status" required class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                                <option value="pending">Pending</option>
                                <option value="completed">Completed</option>
                                <option value="failed">Failed</option>
                                <option value="refunded">Refunded</option>
                                <option value="cancelled">Cancelled</option>
                            </select>
                        </div>

                        <!-- Reference Number -->
                        <div>
                            <label for="reference_number" class="block text-sm font-medium text-gray-700 mb-1">Reference Number</label>
                            <input type="text" name="reference_number" id="reference_number"
                                   class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                                   placeholder="Optional reference">
                        </div>
                    </div>

                    <!-- Notes -->
                    <div>
                        <label for="notes" class="block text-sm font-medium text-gray-700 mb-1">Notes</label>
                        <textarea name="notes" id="notes" rows="3"
                                  class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                                  placeholder="Optional notes about this payment"></textarea>
                    </div>

                    <!-- Modal Actions -->
                    <div class="flex justify-end space-x-3 pt-4">
                        <button type="button" onclick="closeAddPaymentModal()"
                                class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            Cancel
                        </button>
                        <button type="submit"
                                class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            Add Payment
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Edit Payment Modal -->
    <div id="editPaymentModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
        <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">
                        <i class="fas fa-edit text-green-600 mr-2"></i>Edit Payment
                    </h3>
                    <button onclick="closeEditPaymentModal()" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>

                <form method="POST" class="space-y-4" id="editPaymentForm">
                    <input type="hidden" name="action" value="edit_payment">
                    <input type="hidden" name="payment_id" id="editPaymentId">

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <!-- Transaction ID (Read-only) -->
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-700 mb-1">Transaction ID</label>
                            <input type="text" id="editTransactionId" readonly
                                   class="w-full border border-gray-300 rounded-md px-3 py-2 bg-gray-100 text-gray-600 cursor-not-allowed">
                        </div>

                        <!-- Customer Selection -->
                        <div>
                            <label for="editCustomerId" class="block text-sm font-medium text-gray-700 mb-1">Customer *</label>
                            <select name="customer_id" id="editCustomerId" required class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                                <option value="">Select Customer</option>
                                <?php foreach ($customers as $customer): ?>
                                    <option value="<?= $customer['id'] ?>"><?= htmlspecialchars($customer['name']) ?> (<?= htmlspecialchars($customer['email']) ?>)</option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <!-- Order Selection (Optional) -->
                        <div>
                            <label for="editOrderId" class="block text-sm font-medium text-gray-700 mb-1">Order (Optional)</label>
                            <select name="order_id" id="editOrderId" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                                <option value="">No specific order</option>
                                <?php foreach ($orders as $order): ?>
                                    <option value="<?= $order['id'] ?>"><?= htmlspecialchars($order['order_number']) ?> ($<?= number_format($order['total_amount'], 2) ?>)</option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <!-- Amount -->
                        <div>
                            <label for="editAmount" class="block text-sm font-medium text-gray-700 mb-1">Amount *</label>
                            <input type="number" name="amount" id="editAmount" step="0.01" min="0.01" required
                                   class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                                   placeholder="0.00">
                        </div>

                        <!-- Payment Method -->
                        <div>
                            <label for="editPaymentMethod" class="block text-sm font-medium text-gray-700 mb-1">Payment Method *</label>
                            <select name="payment_method" id="editPaymentMethod" required class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                                <option value="">Select Payment Method</option>
                                <option value="shilling_somali">Shilling Somali</option>
                                <option value="evc">EVC</option>
                                <option value="bank_transfer">Bank Transfer</option>
                                <option value="cash_dollar">Cash Dollar</option>
                            </select>
                        </div>

                        <!-- Status -->
                        <div>
                            <label for="editStatus" class="block text-sm font-medium text-gray-700 mb-1">Status *</label>
                            <select name="status" id="editStatus" required class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                                <option value="pending">Pending</option>
                                <option value="completed">Completed</option>
                                <option value="failed">Failed</option>
                                <option value="refunded">Refunded</option>
                                <option value="cancelled">Cancelled</option>
                            </select>
                        </div>

                        <!-- Reference Number -->
                        <div>
                            <label for="editReferenceNumber" class="block text-sm font-medium text-gray-700 mb-1">Reference Number</label>
                            <input type="text" name="reference_number" id="editReferenceNumber"
                                   class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                                   placeholder="Optional reference">
                        </div>
                    </div>

                    <!-- Notes -->
                    <div>
                        <label for="editNotes" class="block text-sm font-medium text-gray-700 mb-1">Notes</label>
                        <textarea name="notes" id="editNotes" rows="3"
                                  class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                                  placeholder="Optional notes about this payment"></textarea>
                    </div>

                    <!-- Modal Actions -->
                    <div class="flex justify-end space-x-3 pt-4">
                        <button type="button" onclick="closeEditPaymentModal()"
                                class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            Cancel
                        </button>
                        <button type="submit"
                                class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                            <i class="fas fa-save mr-2"></i>Update Payment
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- View Payment Details Modal -->
    <div id="viewPaymentModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
        <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">Payment Details</h3>
                    <button onclick="closeViewPaymentModal()" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>

                <div id="paymentDetailsContent" class="space-y-4">
                    <!-- Payment details will be loaded here -->
                </div>

                <div class="flex justify-end pt-4">
                    <button onclick="closeViewPaymentModal()"
                            class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        Close
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div id="deletePaymentModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div class="mt-3 text-center">
                <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
                    <i class="fas fa-exclamation-triangle text-red-600 text-xl"></i>
                </div>
                <h3 class="text-lg font-medium text-gray-900 mt-2">Delete Payment</h3>
                <div class="mt-2 px-7 py-3">
                    <p class="text-sm text-gray-500">
                        Are you sure you want to delete payment <span id="deletePaymentId" class="font-medium"></span>?
                        This action cannot be undone.
                    </p>
                </div>
                <div class="flex justify-center space-x-3 px-4 py-3">
                    <button onclick="closeDeletePaymentModal()"
                            class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        Cancel
                    </button>
                    <form method="POST" style="display: inline;">
                        <input type="hidden" name="action" value="delete_payment">
                        <input type="hidden" name="payment_id" id="deletePaymentIdInput">
                        <button type="submit"
                                class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                            Delete
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript Functions -->
    <script>
        // Modal functions
        function openAddPaymentModal() {
            document.getElementById('addPaymentModal').classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }

        function closeAddPaymentModal() {
            document.getElementById('addPaymentModal').classList.add('hidden');
            document.body.style.overflow = 'auto';
            // Reset form
            document.querySelector('#addPaymentModal form').reset();
        }

        // Close modal when clicking outside
        document.getElementById('addPaymentModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeAddPaymentModal();
            }
        });

        // Close modal when clicking outside - View Payment Modal
        document.getElementById('viewPaymentModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeViewPaymentModal();
            }
        });

        // Close modal when clicking outside - Edit Payment Modal
        document.getElementById('editPaymentModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeEditPaymentModal();
            }
        });

        // Close modal when clicking outside - Delete Payment Modal
        document.getElementById('deletePaymentModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeDeletePaymentModal();
            }
        });

        // Close modals on Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeAddPaymentModal();
                closeEditPaymentModal();
                closeViewPaymentModal();
                closeDeletePaymentModal();
            }
        });

        // Edit Payment Modal functions
        function openEditPaymentModal() {
            document.getElementById('editPaymentModal').classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }

        function closeEditPaymentModal() {
            document.getElementById('editPaymentModal').classList.add('hidden');
            document.body.style.overflow = 'auto';
            // Reset form
            document.getElementById('editPaymentForm').reset();
        }

        function editPayment(paymentId) {
            // Fetch payment data and populate edit form
            fetch(`?action=get_payment&id=${paymentId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        populateEditForm(data.payment);
                        openEditPaymentModal();
                    } else {
                        alert('Error loading payment data: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Error loading payment data. Please try again.');
                });
        }

        function populateEditForm(payment) {
            // Populate form fields with payment data
            document.getElementById('editPaymentId').value = payment.id;
            document.getElementById('editTransactionId').value = payment.transaction_id;
            document.getElementById('editCustomerId').value = payment.customer_id || '';
            document.getElementById('editOrderId').value = payment.order_id || '';
            document.getElementById('editAmount').value = payment.amount;
            document.getElementById('editPaymentMethod').value = payment.payment_method;
            document.getElementById('editStatus').value = payment.status;
            document.getElementById('editReferenceNumber').value = payment.reference_number || '';
            document.getElementById('editNotes').value = payment.notes || '';
        }

        // View Payment Details Modal functions
        function openViewPaymentModal() {
            document.getElementById('viewPaymentModal').classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }

        function closeViewPaymentModal() {
            document.getElementById('viewPaymentModal').classList.add('hidden');
            document.body.style.overflow = 'auto';
        }

        // Delete Payment Modal functions
        function openDeletePaymentModal() {
            document.getElementById('deletePaymentModal').classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }

        function closeDeletePaymentModal() {
            document.getElementById('deletePaymentModal').classList.add('hidden');
            document.body.style.overflow = 'auto';
        }

        function viewPaymentDetails(paymentId) {
            // Get payment details via AJAX
            fetch(`?action=get_payment&id=${paymentId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const payment = data.payment;
                        const statusClasses = {
                            'completed': 'bg-green-100 text-green-800',
                            'pending': 'bg-yellow-100 text-yellow-800',
                            'failed': 'bg-red-100 text-red-800',
                            'refunded': 'bg-gray-100 text-gray-800',
                            'cancelled': 'bg-gray-100 text-gray-800'
                        };
                        const statusClass = statusClasses[payment.status] || 'bg-gray-100 text-gray-800';

                        const methodDisplay = {
                            'credit_card': 'Credit Card',
                            'paypal': 'PayPal',
                            'bank_transfer': 'Bank Transfer',
                            'cash': 'Cash',
                            'mobile_money': 'Mobile Money'
                        };
                        const methodText = methodDisplay[payment.payment_method] || payment.payment_method;

                        document.getElementById('paymentDetailsContent').innerHTML = `
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div class="space-y-3">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">Transaction ID</label>
                                        <p class="text-sm text-gray-900 font-mono">${payment.transaction_id}</p>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">Amount</label>
                                        <p class="text-lg font-semibold text-gray-900">$${parseFloat(payment.amount).toFixed(2)}</p>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">Payment Method</label>
                                        <p class="text-sm text-gray-900">${methodText}</p>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">Status</label>
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${statusClass}">
                                            ${payment.status.charAt(0).toUpperCase() + payment.status.slice(1)}
                                        </span>
                                    </div>
                                </div>
                                <div class="space-y-3">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">Customer</label>
                                        <p class="text-sm text-gray-900">${payment.customer_name || 'N/A'}</p>
                                        <p class="text-xs text-gray-500">${payment.customer_email || ''}</p>
                                        ${payment.customer_phone ? `<p class="text-xs text-gray-500">${payment.customer_phone}</p>` : ''}
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">Order</label>
                                        <p class="text-sm text-gray-900">${payment.order_number || 'No specific order'}</p>
                                        ${payment.order_total ? `<p class="text-xs text-gray-500">Order Total: $${parseFloat(payment.order_total).toFixed(2)}</p>` : ''}
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">Payment Date</label>
                                        <p class="text-sm text-gray-900">${new Date(payment.payment_date).toLocaleString()}</p>
                                    </div>
                                    ${payment.reference_number ? `
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">Reference Number</label>
                                        <p class="text-sm text-gray-900 font-mono">${payment.reference_number}</p>
                                    </div>
                                    ` : ''}
                                </div>
                            </div>
                            ${payment.notes ? `
                            <div class="mt-4">
                                <label class="block text-sm font-medium text-gray-700">Notes</label>
                                <p class="text-sm text-gray-900 bg-gray-50 p-3 rounded-md">${payment.notes}</p>
                            </div>
                            ` : ''}
                            <div class="mt-4 pt-4 border-t border-gray-200">
                                <p class="text-xs text-gray-500">Created: ${new Date(payment.created_at).toLocaleString()}</p>
                            </div>
                        `;
                        openViewPaymentModal();
                    } else {
                        alert('Error loading payment details: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Error loading payment details. Please try again.');
                });
        }

        function deletePayment(paymentId, transactionId) {
            document.getElementById('deletePaymentId').textContent = transactionId;
            document.getElementById('deletePaymentIdInput').value = paymentId;
            openDeletePaymentModal();
        }

        function retryPayment(paymentId) {
            if (confirm('Are you sure you want to retry this payment?')) {
                // You can implement payment retry logic here
                alert('Retry payment for ID: ' + paymentId + '\n\nThis would typically trigger a payment retry process.');
                // Example:
                // fetch('retry_payment.php', {
                //     method: 'POST',
                //     headers: { 'Content-Type': 'application/json' },
                //     body: JSON.stringify({ payment_id: paymentId })
                // }).then(response => response.json()).then(data => {
                //     if (data.success) {
                //         location.reload();
                //     } else {
                //         alert('Error: ' + data.message);
                //     }
                // });
            }
        }

        // Auto-submit search form on Enter key
        document.querySelector('input[name="search"]').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                this.form.submit();
            }
        });

        // Auto-fill amount when order is selected
        document.getElementById('order_id').addEventListener('change', function() {
            const selectedOption = this.options[this.selectedIndex];
            if (selectedOption.value && selectedOption.text.includes('$')) {
                const amountMatch = selectedOption.text.match(/\$([0-9,]+\.?[0-9]*)/);
                if (amountMatch) {
                    const amount = amountMatch[1].replace(',', '');
                    document.getElementById('amount').value = amount;
                }
            }
        });
    </script>
</body>
</html>